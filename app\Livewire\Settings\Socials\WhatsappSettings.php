<?php

namespace App\Livewire\Settings\Socials;

use App\Models\WhatsApp\WhatsAppConnectedUser;
use Livewire\Component;
use Livewire\Attributes\On;
use App\Models\WhatsApp\WhatsAppSettings as WhatsAppSettingsModel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class WhatsappSettings extends Component
{
    public $auto_reply = false;
    public $welcome_message = '';
    public $connected_ai_model = null;
    public $device_info = null;

    // Available AI models (placeholder - you can populate from your AI models)
    public $available_ai_models = [
        1 => 'GPT-4',
        2 => 'Claude 3',
        3 => 'Gemini Pro',
        4 => 'Custom Model'
    ];

    #[On('loadSettings')]
    public function loadSettings()
    {
        $user = Auth::user();
        if ($user) {
            // FAST: Load settings with connection info from database first
            $settings = WhatsAppSettingsModel::forUserWithConnection($user->id);

            if ($settings) {
                // Load basic settings
                $this->auto_reply = $settings->auto_reply;
                $this->welcome_message = $settings->welcome_message ?? '';
                $this->connected_ai_model = $settings->connected_ai_model;

                // FAST: Check if user is connected from database
                $connectedUser = $settings->connectedUser;
                if ($connectedUser) {
                    $this->device_info = [
                        'session_id' => $connectedUser->session_id,
                        'phone_number' => $connectedUser->phone_number,
                        'profile_name' => $connectedUser->profile_name,
                        'profile_picture_url' => $connectedUser->profile_picture_url,
                        'device_model' => $connectedUser->device_model,
                        'platform' => $connectedUser->platform,
                    ];
                } else {
                    $this->device_info = null;
                }
            } else {
                // Create default settings if none exist
                WhatsAppSettingsModel::updateOrCreateForUser($user->id, [
                    'auto_reply' => false,
                    'welcome_message' => '',
                    'connected_ai_model' => null,
                ]);
            }
        }
    }

    /**
     * Refresh device info (called when user gets authenticated)
     */
    #[On('whatsapp-authenticated')]
    public function refreshDeviceInfo()
    {
        try {
            $userId = Auth::id();

            if (!$this->device_info) {
                Log::warning('No device info received in refreshDeviceInfo method');
                return;
            }

            // Use session ID from WhatsApp server
            $sessionId = $this->device_info['session_id'] ?? 'session_' . time() . '_' . $userId;

            // Create device info array in the format expected by updateOrCreateFromClientInfo
            $deviceInfo = [
                'wid' => ['user' => $this->device_info['phone_number']],
                'pushname' => $this->device_info['profile_name'],
                'profilePicUrl' => $this->device_info['profile_picture_url'],
                'phone' => ['device_model' => $this->device_info['device_model']],
                'platform' => $this->device_info['platform']
            ];

            // Save the connected user information
            $connectedUser = WhatsAppConnectedUser::updateOrCreateFromClientInfo(
                $userId,
                $sessionId,
                $deviceInfo
            );

        } catch (\Exception $e) {
            Log::error('Error saving connected WhatsApp user: ' . $e->getMessage(), [
                'user_id' => $userId ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function saveSettings()
    {
        $this->validate([
            'welcome_message' => 'nullable|string|max:1000',
            'connected_ai_model' => 'nullable|integer',
        ]);

        $user = Auth::user();
        if ($user) {
            WhatsAppSettingsModel::updateOrCreateForUser($user->id, [
                'auto_reply' => $this->auto_reply,
                'welcome_message' => $this->welcome_message,
                'connected_ai_model' => $this->connected_ai_model,
            ]);

            $this->dispatch('settings-saved', 'WhatsApp settings saved successfully!');
        }
    }

    public function render()
    {
        return view('livewire.settings.socials.whatsapp-settings');
    }
}

    <div class="space-y-6" x-data="whatsappSettings()" x-init="init()" x-cloak>
        <!-- Header Section -->
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-xl font-semibold text-slate-800 dark:text-navy-50">
                    WhatsApp Integration
                </h3>
                <p class="text-sm text-slate-500 dark:text-navy-300 mt-1">
                    Connect your WhatsApp account to automate messaging and customer support
                </p>
            </div>
            <div class="flex items-center space-x-2">
                <!-- Connection Status Indicator -->
                <div class="flex items-center space-x-2">
                    <div class="relative status-indicator">
                        <div class="w-3 h-3 rounded-full"
                            :class="{
                                'bg-green-500': connectionStatus === 'authenticated',
                                'bg-yellow-500': connectionStatus === 'ready' ||
                                    connectionStatus === 'authenticating' ||
                                    connectionStatus === 'connecting',
                                'bg-red-500': connectionStatus === 'serverUnavailable',
                                'bg-slate-400': !connectionStatus
                            }">
                        </div>
                        <div class="absolute inset-0 w-3 h-3 rounded-full animate-ping"
                            x-show="connectionStatus === 'ready' || connectionStatus === 'authenticating' || connectionStatus === 'connecting'"
                            :class="{
                                'bg-yellow-500': connectionStatus === 'ready' ||
                                    connectionStatus === 'authenticating' ||
                                    connectionStatus === 'connecting'
                            }">
                        </div>
                    </div>
                    <span class="text-sm font-medium"
                        :class="{
                            'text-green-600 dark:text-green-400': connectionStatus === 'authenticated',
                            'text-yellow-600 dark:text-yellow-400': connectionStatus === 'ready' ||
                                connectionStatus === 'authenticating',
                            'text-red-600 dark:text-red-400': connectionStatus === 'serverUnavailable',
                            'text-slate-500 dark:text-slate-400': !connectionStatus
                        }">
                        <span x-show="connectionStatus === 'authenticated'">Connected</span>
                        <span x-show="connectionStatus === 'ready'">Ready to Connect</span>
                        <span x-show="connectionStatus === 'connecting'">Connecting to WhatsApp...</span>
                        <span x-show="connectionStatus === 'authenticating'">Authenticating...</span>
                        <span x-show="connectionStatus === 'serverUnavailable'">Server Unavailable</span>
                        <span x-show="!connectionStatus">Initializing...</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- Connection Section -->
        <div class="card p-6">

            <!-- Authenticating State - Show when authenticating -->
            <div x-show="connectionStatus === 'authenticating'" x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform scale-95"
                x-transition:enter-end="opacity-100 transform scale-100"
                class="bg-slate-50 dark:bg-slate-800/50 rounded-2xl p-8 border border-slate-200 dark:border-slate-700/50 text-center">

                <div class="max-w-md mx-auto">
                    <!-- WhatsApp Icon -->
                    <div
                        class="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fab fa-whatsapp text-3xl text-green-600 dark:text-green-400"></i>
                    </div>

                    <!-- Title -->
                    <h3 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">
                        Authenticating...
                    </h3>

                    <!-- Message -->
                    <p class="text-sm text-slate-600 dark:text-slate-400 mb-6">
                        QR code scanned! Completing authentication...
                    </p>

                    <!-- Loading Animation -->
                    <div class="flex items-center justify-center space-x-1">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-bounce" style="animation-delay: 0.1s">
                        </div>
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-bounce" style="animation-delay: 0.2s">
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code Section - Show when ready and QR code is available -->
            <div x-show="connectionStatus === 'ready' && showQrCode"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform scale-95"
                x-transition:enter-end="opacity-100 transform scale-100"
                class="bg-slate-100 dark:bg-slate-800/50 rounded-2xl p-8 border border-slate-200 dark:border-slate-700/50">

                <div class="flex items-center justify-between max-w-5xl mx-auto">
                    <!-- QR Code Container -->
                    <div class="flex-shrink-0">
                        <div
                            class="w-72 h-72 bg-white dark:bg-slate-700 rounded-2xl p-6 border border-slate-300 dark:border-slate-600/50 shadow-lg">
                            <div
                                class="w-full h-full bg-white dark:bg-white rounded-xl flex items-center justify-center relative">
                                <!-- Real QR Code -->
                                <div x-show="showQrCode" class="w-full h-full flex items-center justify-center">
                                    <img :src="showQrCode" alt="WhatsApp QR Code"
                                        class="w-full h-full object-contain rounded-lg">
                                </div>

                                <!-- Loading State -->
                                <div x-show="!showQrCode" class="relative flex items-center justify-center">
                                    <!-- QR Code Icon Background -->
                                    <div class="w-32 h-32 bg-slate-100 rounded-2xl flex items-center justify-center">
                                        <i class="fas fa-qrcode text-6xl text-slate-400"></i>
                                    </div>

                                    <!-- Circular Progress Indicator -->
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div
                                            class="w-20 h-20 rounded-full border-4 border-slate-200 border-t-emerald-500 animate-spin bg-white/90">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="flex-1 ml-12">
                        <h2 class="text-3xl font-bold text-slate-800 dark:text-slate-200 mb-4">
                            Scan QR Code
                        </h2>
                        <p class="text-slate-600 dark:text-slate-300 text-lg leading-relaxed mb-6">
                            Open WhatsApp on your phone, go to Settings > Linked Devices > Link a Device, and scan this
                            QR
                            code
                        </p>

                        <!-- Loading Animation -->
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-emerald-500 rounded-full animate-bounce"></div>
                            <div class="w-3 h-3 bg-emerald-500 rounded-full animate-bounce"
                                style="animation-delay: 0.1s">
                            </div>
                            <div class="w-3 h-3 bg-emerald-500 rounded-full animate-bounce"
                                style="animation-delay: 0.2s">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Connected Device Info - Professional Design -->
            <div x-show="connectionStatus === 'authenticated' && device_info"
                x-transition:enter="transition ease-out duration-500"
                x-transition:enter-start="opacity-0 transform translate-y-4"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                class="relative overflow-hidden bg-white dark:bg-slate-800/95 backdrop-blur-sm border border-slate-200 dark:border-slate-700/50 rounded-2xl shadow-lg">

                <!-- Subtle Background Pattern -->
                <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent"></div>

                <!-- Content -->
                <div class="relative p-6">
                    <div class="flex items-center justify-between">
                        <!-- Left: Profile & Status -->
                        <div class="flex items-center space-x-5">
                            <!-- Enhanced Profile Image -->
                            <div class="relative group">
                                <!-- Subtle Glow Effect -->
                                <div
                                    class="absolute -inset-1 bg-gradient-to-r from-emerald-500/10 to-emerald-600/10 rounded-full blur opacity-20 group-hover:opacity-30 transition duration-300">
                                </div>

                                <!-- Profile Image -->
                                <div class="relative">
                                    <img :src="(device_info && device_info.profile_picture_url) ? device_info.profile_picture_url:
                                        `https://ui-avatars.com/api/?name=${encodeURIComponent((device_info && device_info.profile_name) || (device_info && device_info.phone_number) || 'User')}&background=6b7280&color=fff&size=56&rounded=true&bold=true`"
                                        alt="WhatsApp Profile"
                                        class="w-14 h-14 rounded-full border-2 border-slate-300 dark:border-slate-600/50 shadow-lg relative z-10 object-cover"
                                        x-on:error="$event.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent((device_info && device_info.profile_name) || (device_info && device_info.phone_number) || 'User')}&background=6b7280&color=fff&size=56&rounded=true&bold=true`">

                                    <!-- Status Indicator -->
                                    <div
                                        class="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-emerald-500 rounded-full flex items-center justify-center border-2 border-white dark:border-slate-800 shadow-lg z-20">
                                        <div class="w-1.5 h-1.5 bg-white rounded-full"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Connection Details -->
                            <div class="space-y-1">
                                <div class="flex items-center space-x-2">
                                    <h4 class="text-lg font-medium text-slate-800 dark:text-slate-200 tracking-tight">
                                        <span
                                            x-text="(device_info && device_info.profile_name) || 'WhatsApp User'"></span>
                                    </h4>
                                    <div class="flex items-center space-x-1">
                                        <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                        <span
                                            class="text-xs font-medium text-emerald-600 dark:text-emerald-500 uppercase tracking-wider">Connected</span>
                                    </div>
                                </div>

                                <p class="text-sm text-slate-600 dark:text-slate-400 font-medium">
                                    <span
                                        x-text="(device_info && device_info.phone_number) || 'Phone number not available'"></span>
                                </p>

                                <!-- Simple Device Info -->
                                <div class="flex items-center space-x-3 mt-2">
                                    <!-- Device Name -->
                                    <div
                                        class="inline-flex items-center px-2.5 py-1 bg-slate-100 dark:bg-slate-800/40 border border-slate-200 dark:border-slate-700/50 rounded-full">
                                        <i
                                            class="fas fa-mobile-alt text-slate-500 dark:text-slate-400 text-xs mr-1.5"></i>
                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-400"
                                            x-text="(device_info && device_info.device_model) || 'Unknown Device'"></span>
                                    </div>

                                    <!-- Device Type -->
                                    <div
                                        class="inline-flex items-center px-2.5 py-1 bg-slate-100 dark:bg-slate-800/40 border border-slate-200 dark:border-slate-700/50 rounded-full">
                                        <i
                                            class="fas fa-desktop text-slate-500 dark:text-slate-400 text-xs mr-1.5"></i>
                                        <span class="text-xs font-medium text-slate-600 dark:text-slate-400"
                                            x-text="(device_info && device_info.platform) || 'Web'"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right: Session Info & Actions -->
                        <div class="text-right space-y-3">
                            <!-- Session ID -->
                            <div
                                class="inline-flex items-center px-3 py-1.5 bg-slate-100 dark:bg-slate-800/40 border border-slate-200 dark:border-slate-600/30 rounded-lg">
                                <i class="fas fa-key text-emerald-600 dark:text-emerald-400 text-xs mr-2"></i>
                                <span class="text-xs font-mono text-slate-700 dark:text-slate-300"
                                    x-text="(device_info && device_info.session_id) || 'No Session'"></span>
                            </div>

                            <!-- Disconnect Button -->
                            <div>
                                <button @click="disconnectWhatsApp()"
                                    class="group inline-flex items-center px-4 py-2.5 bg-red-500/90 hover:bg-red-500 border border-red-400/30 rounded-lg font-medium text-white text-sm transition-all duration-200 hover:shadow-lg hover:shadow-red-500/25 focus:outline-none focus:ring-2 focus:ring-red-500/50">
                                    <i
                                        class="fas fa-power-off mr-2 text-xs group-hover:rotate-180 transition-transform duration-300"></i>
                                    Disconnect
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Simple Connection Status -->
                    <div class="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700/30">
                        <div class="flex items-center justify-between text-xs">
                            <div class="flex items-center space-x-1.5">
                                <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                <span class="text-slate-600 dark:text-slate-500 font-medium">Status:</span>
                                <span class="text-slate-700 dark:text-slate-300 font-medium capitalize"
                                    x-text="connectionStatus || 'disconnected'"></span>
                            </div>
                            <div class="flex items-center space-x-1.5">
                                <i class="fas fa-shield-alt text-slate-500 dark:text-slate-400"></i>
                                <span class="text-slate-600 dark:text-slate-400 font-medium">Encrypted</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Server Unavailable State -->
            <div x-show="connectionStatus === 'serverUnavailable'"
                class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-6 text-center">

                <div
                    class="w-16 h-16 bg-red-200 dark:bg-red-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-server text-2xl text-red-600 dark:text-red-400"></i>
                </div>
                <h5 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                    WhatsApp Server Unavailable
                </h5>
                <p class="text-sm text-red-600 dark:text-red-300 mb-4"
                    x-text="serverMessage || 'WhatsApp server is currently unavailable. Please try again later.'">
                </p>
                <div class="flex justify-center">
                    <button @click="connectWhatsApp()"
                        class="btn bg-red-500 font-medium text-white hover:bg-red-600 focus:bg-red-600 active:bg-red-600/90">
                        <i class="fas fa-redo mr-2"></i>
                        Retry Connection
                    </button>
                </div>
            </div>

            <!-- Connecting State - Active connection -->
            <div x-show="connectionStatus === 'connecting' || !connectionStatus"
                class="bg-slate-50 dark:bg-slate-800/50 rounded-2xl p-8 border border-slate-200 dark:border-slate-700/50 text-center">

                <div class="max-w-md mx-auto">
                    <!-- WhatsApp Icon -->
                    <div
                        class="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fab fa-whatsapp text-3xl text-green-600 dark:text-green-400"></i>
                    </div>

                    <!-- Title -->
                    <h3 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">
                        Connecting to WhatsApp...
                    </h3>

                    <!-- Message -->
                    <p class="text-sm text-slate-600 dark:text-slate-400 mb-6">
                        Establishing connection to WhatsApp server, please wait...
                    </p>

                    <!-- Loading Animation -->
                    <div class="flex items-center justify-center space-x-1">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-bounce" style="animation-delay: 0.1s">
                        </div>
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-bounce" style="animation-delay: 0.2s">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Settings Toggle -->
        <div class="flex flex-col items-center space-y-3">
            <div class="text-center">
                <p class="text-sm text-slate-500 dark:text-navy-300">
                    Configure auto-reply, welcome messages, and AI integration
                </p>
            </div>
            <button @click="showAdvancedSettings = !showAdvancedSettings"
                class="btn bg-slate-150 font-medium text-slate-800 hover:bg-slate-200 focus:bg-slate-200 active:bg-slate-200/80 dark:bg-navy-500 dark:text-navy-50 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90 shadow-lg hover:shadow-xl transition-all duration-200">
                <i class="fas fa-cog mr-2"></i>
                <span x-show="!showAdvancedSettings">Show Advanced Settings</span>
                <span x-show="showAdvancedSettings">Hide Advanced Settings</span>
                <i class="fas fa-chevron-down ml-2 transition-transform duration-200"
                    :class="{ 'rotate-180': showAdvancedSettings }"></i>
            </button>
        </div>

        <!-- Settings Configuration -->
        <div x-show="showAdvancedSettings" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-95"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-95" class="card p-6">
            <h4 class="text-lg font-semibold text-slate-800 dark:text-navy-50 mb-6">
                WhatsApp Settings
            </h4>

            <div class="space-y-6">
                <!-- Auto Reply Toggle -->
                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-slate-700 dark:text-navy-100">
                            Auto Reply
                        </label>
                        <p class="text-xs text-slate-500 dark:text-navy-300 mt-1">
                            Automatically respond to incoming messages
                        </p>
                    </div>
                    <label class="inline-flex items-center">
                        <input type="checkbox" wire:model.live="auto_reply"
                            class="form-switch h-5 w-10 rounded-full bg-slate-300 before:rounded-full before:bg-slate-50 checked:bg-primary checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-accent dark:checked:before:bg-white">
                    </label>
                </div>

                <!-- Welcome Message -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-navy-100 mb-2">
                        Welcome Message
                    </label>
                    <textarea wire:model.defer="welcome_message" rows="3"
                        placeholder="Enter your welcome message for new contacts..."
                        class="form-textarea w-full resize-none rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"></textarea>
                    <p class="text-xs text-slate-500 dark:text-navy-300 mt-1">
                        This message will be sent automatically to new contacts
                    </p>
                </div>

                <!-- AI Model Selection -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-navy-100 mb-2">
                        Connected AI Model
                    </label>
                    <select wire:model.defer="connected_ai_model"
                        class="form-select w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                        <option value="">Select an AI model</option>
                        @foreach ($available_ai_models as $id => $name)
                            <option value="{{ $id }}">{{ $name }}</option>
                        @endforeach
                    </select>
                    <p class="text-xs text-slate-500 dark:text-navy-300 mt-1">
                        Choose which AI model to use for automated responses
                    </p>
                </div>

                <!-- Save Settings Button -->
                <div class="flex justify-end pt-4 border-t border-slate-200 dark:border-navy-600">
                    <button wire:click="saveSettings" wire:loading.attr="disabled" wire:target="saveSettings"
                        class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                        <span wire:loading.remove wire:target="saveSettings">
                            <i class="fas fa-save mr-2"></i>
                            Save Settings
                        </span>
                        <span wire:loading wire:target="saveSettings">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Saving...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    @script
        <script>
            // WhatsApp Settings Alpine.js Component
            window.whatsappSettings = function() {
                return {
                    // Local properties
                    showQrCode: null,
                    connectionStatus: null,
                    device_info: null,
                    showAdvancedSettings: false,
                    serverMessage: '', // For server unavailable messages

                    // Initialize component
                    init() {
                        console.log('🚀 WhatsApp component initializing...');
                        // Start WhatsApp connection
                        this.connectWhatsApp();

                        // Setup cleanup when component is disposed
                        this.setupCleanup();
                    },

                    // Setup cleanup handlers
                    setupCleanup() {
                        // Handle page unload/navigation
                        const cleanup = () => {
                            console.log('🧹 Page unload - stopping heartbeat');
                            this.stopHeartbeat();
                        };

                        // Handle browser close/refresh
                        window.addEventListener('beforeunload', cleanup);

                        // Store cleanup function for manual call
                        this.cleanup = cleanup;
                    },

                    // Stop heartbeat
                    stopHeartbeat() {
                        if (window.whatsappAuth && window.whatsappAuth.stopWhatsAppHeartbeat) {
                            window.whatsappAuth.stopWhatsAppHeartbeat();
                        }
                    },

                    // Manual destroy method - stop heartbeat when leaving WhatsApp settings
                    destroy() {
                        console.log('🗑️ WhatsApp component destroyed - stopping heartbeat');
                        this.stopHeartbeat();
                        if (this.cleanup) {
                            window.removeEventListener('beforeunload', this.cleanup);
                        }
                    },

                    // Connect WhatsApp
                    connectWhatsApp() {
                        this.connectionStatus = 'connecting'; // Set default status to connecting
                        try {
                            window.whatsappAuth.connectWhatsApp({
                                onConnecting: (message) => {
                                    this.connectionStatus = 'connecting';
                                    this.showQrCode = null;
                                    console.log('🔄 Connecting:', message);
                                },
                                onReady: (qrCode) => {
                                    this.showQrCode = qrCode;
                                    this.connectionStatus = 'ready';
                                },
                                onAuthenticating: (message) => {
                                    this.connectionStatus = 'authenticating';
                                    this.showQrCode = null;
                                },
                                onAuthenticated: (deviceInfo) => {
                                    console.log('✅ Authenticated - Raw device info:', deviceInfo);
                                    console.log('🔑 Session ID candidates:', {
                                        session: deviceInfo?.session,
                                        sessionId: deviceInfo?.sessionId,
                                        id: deviceInfo?.id,
                                        wid_serialized: deviceInfo?.wid?._serialized,
                                        wid_user: deviceInfo?.wid?.user,
                                        wid_full: deviceInfo?.wid
                                    });

                                    this.connectionStatus = 'authenticated';
                                    this.showQrCode = null;

                                    // Store device info locally for immediate display
                                    if (deviceInfo) {
                                        this.device_info = {
                                            session_id: deviceInfo.wid?._serialized || deviceInfo.wid
                                                ?.user + '@c.us' || 'session_' + Date.now(),
                                            phone_number: deviceInfo.wid?.user || deviceInfo
                                                .phone_number ||
                                                'Unknown',
                                            profile_name: deviceInfo.pushname || deviceInfo
                                                .profile_name ||
                                                'WhatsApp User',
                                            profile_picture_url: deviceInfo.profilePicUrl || deviceInfo
                                                .profile_picture_url || null,
                                            device_model: deviceInfo.phone?.device_model || deviceInfo
                                                .device_model || 'Unknown Device',
                                            platform: deviceInfo.platform || 'Unknown Platform'
                                        };
                                        $wire.device_info = this.device_info;
                                        // Dispatch Livewire event with individual parameters
                                        $wire.dispatch('whatsapp-authenticated');
                                    } else {
                                        console.warn('No device info received in onAuthenticated callback');
                                    }
                                },
                                onServerUnavailable: (message) => {
                                    console.log('🚫 Server unavailable:', message);
                                    this.connectionStatus = 'serverUnavailable';
                                    this.serverMessage = message;
                                    this.showQrCode = null;
                                }
                            });
                        } catch (error) {
                            console.error('❌ Failed to connect WhatsApp:', error);
                            this.connectionStatus = 'connecting'; // Default to connecting instead of unauthenticated
                        }
                    },

                    // Disconnect WhatsApp
                    async disconnectWhatsApp() {
                        console.log('🔌 Disconnecting WhatsApp...');

                        try {
                            // Use WhatsApp auth module to disconnect
                            if (window.whatsappAuth && window.whatsappAuth.disconnectWhatsApp) {
                                await window.whatsappAuth.disconnectWhatsApp();

                                console.log('✅ WhatsApp disconnected successfully');

                                // Reset component state
                                this.connectionStatus = 'connecting';
                                this.showQrCode = null;
                                this.device_info = null;

                                // Clear Livewire device info
                                $wire.device_info = null;

                                // Show success notification
                                if (window.$notification) {
                                    window.$notification({
                                        text: 'WhatsApp disconnected successfully',
                                        variant: 'success',
                                        position: 'top-right'
                                    });
                                }

                                // Reconnect after a short delay
                                setTimeout(() => {
                                    this.connectWhatsApp();
                                }, 2000);

                            } else {
                                throw new Error('WhatsApp auth module not available');
                            }

                        } catch (error) {
                            console.error('❌ Error disconnecting WhatsApp:', error);

                            // Show error notification
                            if (window.$notification) {
                                window.$notification({
                                    text: 'Failed to disconnect WhatsApp: ' + error.message,
                                    variant: 'error',
                                    position: 'top-right'
                                });
                            }
                        }
                    },
                };
            }

            // Listen for Livewire events
            document.addEventListener('livewire:init', () => {
                Livewire.on('settings-saved', (message) => {
                    console.log('✅ WhatsApp settings saved:', message);
                    // Show success notification
                    if (window.$notification) {
                        window.$notification({
                            text: message,
                            variant: 'success',
                            position: 'top-right'
                        });
                    }
                });
            });
        </script>
    @endscript
